import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import PostCard from '../components/PostCard';
import ProfileEditModal from '../components/ProfileEditModal';
import { User } from '../types';

const UserProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const { currentUser, users, isAdmin } = useAuth();
  const { posts } = usePosts();
  const navigate = useNavigate();
  const [showEditModal, setShowEditModal] = useState(false);
  const [profileUser, setProfileUser] = useState<User | null>(null);

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    if (!userId) {
      navigate('/404');
      return;
    }

    // Find the user whose profile we're viewing
    const user = users.find(u => u.id === userId);
    if (!user) {
      navigate('/404');
      return;
    }

    setProfileUser(user);
  }, [userId, users, currentUser, navigate]);

  if (!currentUser || !profileUser) {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
        <p className="text-lg text-neutral-100">Loading profile...</p>
      </div>
    );
  }

  // Check if current user can edit this profile
  const canEdit = currentUser.id === profileUser.id || isAdmin;
  const isOwnProfile = currentUser.id === profileUser.id;

  // Get user's posts (including anonymous posts if admin is viewing)
  const userPosts = posts.filter(post => {
    if (isAdmin) {
      // Admin can see all posts by this user, including anonymous ones
      return post.userId === profileUser.id || post.actualUserId === profileUser.id;
    } else if (isOwnProfile) {
      // User can see their own posts, including anonymous ones
      return post.userId === profileUser.id || post.actualUserId === profileUser.id;
    } else {
      // Others can only see non-anonymous posts
      return post.userId === profileUser.id && !post.isAnonymous;
    }
  });

  return (
    <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <header className="flex flex-col sm:flex-row items-center mb-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border animate-slide-up hover-glow cyber-border">
        <img
          src={profileUser.avatarUrl}
          alt={profileUser.username}
          className="w-24 h-24 sm:w-32 sm:h-32 rounded-full mr-0 sm:mr-8 mb-4 sm:mb-0 border-4 border-brand-primary hover-scale transition-transform duration-200 animate-pulse-glow"
        />
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-3xl sm:text-4xl font-bold text-neutral-100 text-center sm:text-left animate-text-glow hover-text-glow">
              {profileUser.username}
            </h1>
            {canEdit && (
              <button
                onClick={() => setShowEditModal(true)}
                className="bg-brand-primary hover:bg-brand-secondary text-white text-sm font-semibold py-1.5 px-3 rounded-md transition-all duration-200 hover-scale hover-glow"
              >
                {isOwnProfile ? 'Edit Profile' : 'Edit User (Admin)'}
              </button>
            )}
          </div>

          {/* Bio Section */}
          {profileUser.bio ? (
            <div className="mb-4 text-center sm:text-left">
              <p className="text-neutral-200 text-sm whitespace-pre-line leading-relaxed">
                {profileUser.bio}
              </p>
            </div>
          ) : (
            <div className="mb-4 text-center sm:text-left">
              <p className="text-neutral-muted text-sm italic">
                {isOwnProfile ? 'No bio yet. Click "Edit Profile" to add one.' : 'No bio available.'}
              </p>
            </div>
          )}

          <div className="mt-4 flex space-x-6 justify-center sm:justify-start">
            <div className="text-center">
              <span className="font-bold text-xl text-neutral-100">{userPosts.length}</span>
              <p className="text-neutral-muted text-sm">Posts</p>
            </div>
            <div className="text-center">
              <span className="font-bold text-xl text-neutral-100">0</span>
              <p className="text-neutral-muted text-sm">Followers</p>
            </div>
            <div className="text-center">
              <span className="font-bold text-xl text-neutral-100">0</span>
              <p className="text-neutral-muted text-sm">Following</p>
            </div>
          </div>

          {profileUser.id === 'admin_s3kt0r_truth' && (
            <p className="mt-3 text-sm bg-red-500/20 text-red-400 px-3 py-1 rounded-md inline-block">ADMINISTRATOR ACCESS</p>
          )}

          {!profileUser.isActive && (
            <p className="mt-3 text-sm bg-yellow-500/20 text-yellow-400 px-3 py-1 rounded-md inline-block">INACTIVE USER</p>
          )}

          {profileUser.isPendingApproval && (
            <p className="mt-3 text-sm bg-blue-500/20 text-blue-400 px-3 py-1 rounded-md inline-block">PENDING APPROVAL</p>
          )}
        </div>
      </header>

      <h2 className="text-2xl font-semibold text-neutral-100 mb-6 hover-text-glow transition-all duration-300">
        {isOwnProfile ? 'Your Posts' : `${profileUser.username}'s Posts`}
      </h2>

      {userPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {userPosts.map((post, index) => (
            <div key={post.id} style={{ animationDelay: `${index * 0.05}s` }}>
              <PostCard post={post} isCompact={true} />
            </div>
          ))}
        </div>
      ) : (
        <p className="text-neutral-muted text-center py-10 animate-slide-up">
          {isOwnProfile ? (
            <>You haven't made any posts yet. <Link to="/create" className="text-brand-primary hover:underline hover-text-glow transition-all duration-200">Share your truth!</Link></>
          ) : (
            `${profileUser.username} hasn't made any posts yet.`
          )}
        </p>
      )}

      {/* Profile Edit Modal */}
      {showEditModal && profileUser && (
        <ProfileEditModal
          user={profileUser}
          onClose={() => setShowEditModal(false)}
          isAdmin={isAdmin && !isOwnProfile}
        />
      )}
    </div>
  );
};

export default UserProfilePage;
