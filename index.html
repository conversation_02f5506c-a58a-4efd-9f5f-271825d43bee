<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>S3Kt0R-Gram</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Inter', 'sans-serif'],
            'logo': ['"Major Mono Display"', 'monospace'],
          },
          colors: {
            'brand-primary': '#00DC82',
            'brand-secondary': '#00B86B',
            'neutral-base': '#121212',
            'neutral-surface': '#1E1E1E',
            'neutral-border': '#333333',
            'neutral-muted': '#A0A0A0',
            'neutral-100': '#FFFFFF',
            'neutral-200': '#E0E0E0',
            'accent-error': '#FF4D4D',
            'accent-warning': '#FFA500',
            'accent-success': '#32CD32',
            'brand-purple': '#A020F0', // Added for charts & matrix
          }
        }
      }
    }
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Major+Mono+Display&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "uuid": "https://esm.sh/uuid@^11.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "chart.js": "https://esm.sh/chart.js@4.4.3/auto"
  }
}
</script>
<style>
  #matrix-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1; /* Behind all other content */
  }
  body {
    background-color: #121212; /* neutral-base, Fallback if canvas fails or for brief moments */
    color: var(--neutral-100);
  }
  #root {
    position: relative; /* Ensure content is layered above the canvas */
    z-index: 1;
  }
  /* Custom scrollbar for a more thematic feel */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #1E1E1E; /* neutral-surface */
  }
  ::-webkit-scrollbar-thumb {
    background: #00DC82; /* brand-primary */
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #00B86B; /* brand-secondary */
  }

  /* Anarcho-themed animations and effects */
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(0, 220, 130, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(0, 220, 130, 0.6), 0 0 30px rgba(0, 220, 130, 0.4);
    }
  }

  @keyframes text-glow {
    0%, 100% {
      text-shadow: 0 0 5px rgba(0, 220, 130, 0.5);
    }
    50% {
      text-shadow: 0 0 10px rgba(0, 220, 130, 0.8), 0 0 20px rgba(0, 220, 130, 0.6);
    }
  }

  @keyframes scale-in {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes slide-up {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes cyber-flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
    75% { opacity: 0.9; }
  }

  .animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .animate-text-glow {
    animation: text-glow 2s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slide-up 0.4s ease-out;
  }

  .animate-cyber-flicker {
    animation: cyber-flicker 0.1s ease-in-out infinite;
  }

  /* Hover effects for anarcho theme */
  .hover-glow:hover {
    box-shadow: 0 0 15px rgba(0, 220, 130, 0.5);
    transition: box-shadow 0.3s ease;
  }

  .hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
  }

  .hover-text-glow:hover {
    text-shadow: 0 0 8px rgba(0, 220, 130, 0.7);
    transition: text-shadow 0.3s ease;
  }

  /* Cyberpunk border effects */
  .cyber-border {
    position: relative;
    border: 1px solid #333333;
  }

  .cyber-border::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #00DC82, #A020F0, #00FFFF, #FF0040);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .cyber-border:hover::before {
    opacity: 0.7;
  }

  /* Text truncation utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive grid improvements */
  @media (max-width: 640px) {
    .grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  /* Image hover effects for compact cards */
  .compact-image-hover {
    position: relative;
    overflow: hidden;
  }

  .compact-image-hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 220, 130, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .compact-image-hover:hover::after {
    opacity: 1;
  }
</style>
</head>
<body>
  <canvas id="matrix-canvas"></canvas>
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
