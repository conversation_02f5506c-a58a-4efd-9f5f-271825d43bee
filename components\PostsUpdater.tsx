import { useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';

const PostsUpdater: React.FC = () => {
  const { currentUser } = useAuth();
  const { updatePostsWithCurrentUser } = usePosts();

  useEffect(() => {
    updatePostsWithCurrentUser(currentUser?.id || null);
  }, [currentUser?.id, updatePostsWithCurrentUser]);

  return null; // This component doesn't render anything
};

export default PostsUpdater;
