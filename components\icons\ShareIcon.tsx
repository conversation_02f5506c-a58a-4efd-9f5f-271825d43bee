
import React from 'react';

interface IconProps {
  className?: string;
}

const ShareIcon: React.FC<IconProps> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M7.217 10.907a2.25 2.25 0 100 2.186m0-2.186c.195.025.39.042.586.042h4.4c.196 0 .39-.017.585-.042M3 12a9.003 9.003 0 008.652 8.944m-.718-1.982a8.938 8.938 0 01-.062-.027c-.088-.032-.172-.068-.254-.108M12.01 19.5c-3.862 0-7.181-2.408-8.652-5.944M21 12a9.003 9.003 0 00-8.652-8.944m-.718 1.982c-.088.032-.172.068-.254.108" />
  </svg>
);
export default ShareIcon;
