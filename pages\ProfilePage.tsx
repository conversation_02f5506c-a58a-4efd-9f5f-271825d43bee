
import React, { useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

const ProfilePage: React.FC = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!currentUser) {
      navigate('/login'); // Redirect to login if no user is logged in
    } else {
      // Redirect to the user's profile page
      navigate(`/user/${currentUser.id}`, { replace: true });
    }
  }, [currentUser, navigate]);

  // This component now just redirects to /user/:userId
  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
      <p className="text-lg text-neutral-100">Redirecting to your profile...</p>
      <div className="mt-4 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
      </div>
    </div>
  );
};

export default ProfilePage;
