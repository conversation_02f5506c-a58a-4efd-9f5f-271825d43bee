import { createAdminUser } from '../services/firebaseService';

const setupAdmin = async () => {
  try {
    console.log('Setting up admin account...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'Tr1p$t0p3301';
    const adminUsername = 'S3Kt0R_Overseer';
    
    const adminUser = await createAdminUser(adminEmail, adminPassword, adminUsername);
    
    console.log('Admin account created successfully:', adminUser);
    console.log('Admin can now log in with:');
    console.log('Email:', adminEmail);
    console.log('Password:', adminPassword);
    
  } catch (error) {
    console.error('Error setting up admin account:', error);
  }
};

// Run the setup if this file is executed directly
if (require.main === module) {
  setupAdmin();
}

export default setupAdmin;
