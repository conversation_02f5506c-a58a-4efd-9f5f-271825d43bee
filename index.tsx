import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { startMatrixRain } from './matrixRain'; // Import the matrix rain utility

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// Start the Matrix rain effect after the app is rendered
startMatrixRain();
