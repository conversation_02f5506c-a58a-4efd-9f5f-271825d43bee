rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User avatars - users can upload their own avatars
    match /avatars/{userId}/{allPaths=**} {
      allow read: if true; // Anyone can read avatars
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Post images - users can upload images for their posts
    match /posts/{userId}/{allPaths=**} {
      allow read: if true; // Anyone can read post images
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Admin uploads - only admins can upload to admin folder
    match /admin/{allPaths=**} {
      allow read: if true; // Anyone can read admin uploads (like login screen images)
      allow write: if request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
  }
}
