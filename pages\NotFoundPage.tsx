
import React from 'react';
import { Link } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-neutral-base text-center px-4">
      <h1 className="text-6xl font-bold text-brand-primary mb-4">404</h1>
      <p className="text-2xl text-neutral-100 mb-2">Page Not Found</p>
      <p className="text-neutral-muted mb-8">The truth you seek is not here. Or maybe it was never meant to be found.</p>
      <Link 
        to="/" 
        className="bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-3 px-6 rounded-md transition-colors text-lg"
      >
        Return to S3kt)r-Gram
      </Link>
    </div>
  );
};

export default NotFoundPage;
