
import React, { useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import { Chart, registerables } from 'chart.js';
// import { User } from '../types'; // Unused for now

Chart.register(...registerables);

const AdminAnalyticsPage: React.FC = () => {
  const { users, isAdmin } = useAuth();
  const { posts } = usePosts();
  const navigate = useNavigate();

  const userStatusChartRef = useRef<HTMLCanvasElement>(null);
  // const userActivityChartRef = useRef<HTMLCanvasElement>(null); // Example for future chart

  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  useEffect(() => {
    if (!isAdmin) return;

    // User Status Doughnut Chart
    if (userStatusChartRef.current) {
      const activeUsers = users.filter(u => u.isActive && !u.isPendingApproval).length;
      const pendingUsers = users.filter(u => u.isPendingApproval).length;
      const inactiveUsers = users.filter(u => !u.isActive && !u.isPendingApproval).length;

      const existingChart = Chart.getChart(userStatusChartRef.current);
      if (existingChart) {
        existingChart.destroy();
      }

      new Chart(userStatusChartRef.current, {
        type: 'doughnut',
        data: {
          labels: ['Active Users', 'Pending Approval', 'Inactive Users'],
          datasets: [{
            label: 'User Status',
            data: [activeUsers, pendingUsers, inactiveUsers],
            backgroundColor: [
              'rgba(0, 220, 130, 0.7)', // brand-primary (green)
              'rgba(255, 165, 0, 0.7)', // accent-warning (orange)
              'rgba(255, 77, 77, 0.7)',  // accent-error (red)
            ],
            borderColor: [
              '#00DC82',
              '#FFA500',
              '#FF4D4D',
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: { color: '#E0E0E0' } // neutral-200
            },
            title: {
              display: true,
              text: 'User Account Status',
              color: '#FFFFFF', // neutral-100
              font: { size: 16 }
            }
          }
        }
      });
    }
    // Future charts can be initialized here (e.g., userActivityChartRef)

  }, [users, isAdmin]); // Re-render charts if users data changes


  if (!isAdmin) {
    return null; // Or a loading/redirecting message
  }

  const totalUsers = users.length;
  const totalPosts = posts.length;
  const averagePostsPerUser = totalUsers > 0 ? (totalPosts / users.filter(u => u.isActive && !u.isPendingApproval).length).toFixed(2) : 0;
  const averageLikesPerPost = posts.length > 0 ? (posts.reduce((sum, post) => sum + post.likes, 0) / posts.length).toFixed(2) : 0;
  const averageCommentsPerPost = posts.length > 0 ? (posts.reduce((sum, post) => sum + post.comments.length, 0) / posts.length).toFixed(2) : 0;


  return (
    <div className="max-w-6xl mx-auto py-8 px-2 sm:px-4 lg:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-brand-primary text-center sm:text-left">Platform Analytics</h1>
        <Link
            to="/admin"
            className="mt-4 sm:mt-0 bg-neutral-muted hover:bg-neutral-border text-white font-semibold py-2 px-4 rounded-md transition-colors"
        >
            Back to Admin Panel
        </Link>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
        <div className="bg-neutral-surface p-6 rounded-lg shadow-xl border border-neutral-border text-center">
          <h3 className="text-xl font-semibold text-brand-primary mb-2">Total Users</h3>
          <p className="text-4xl font-bold text-neutral-100">{totalUsers}</p>
        </div>
        <div className="bg-neutral-surface p-6 rounded-lg shadow-xl border border-neutral-border text-center">
          <h3 className="text-xl font-semibold text-brand-primary mb-2">Total Posts</h3>
          <p className="text-4xl font-bold text-neutral-100">{totalPosts}</p>
        </div>
        <div className="bg-neutral-surface p-6 rounded-lg shadow-xl border border-neutral-border text-center">
          <h3 className="text-xl font-semibold text-brand-primary mb-2">Avg. Posts/Active User</h3>
          <p className="text-4xl font-bold text-neutral-100">{averagePostsPerUser}</p>
        </div>
         <div className="bg-neutral-surface p-6 rounded-lg shadow-xl border border-neutral-border text-center">
          <h3 className="text-xl font-semibold text-brand-primary mb-2">Avg. Likes/Post</h3>
          <p className="text-4xl font-bold text-neutral-100">{averageLikesPerPost}</p>
        </div>
         <div className="bg-neutral-surface p-6 rounded-lg shadow-xl border border-neutral-border text-center">
          <h3 className="text-xl font-semibold text-brand-primary mb-2">Avg. Comments/Post</h3>
          <p className="text-4xl font-bold text-neutral-100">{averageCommentsPerPost}</p>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-neutral-surface p-4 sm:p-6 rounded-lg shadow-xl border border-neutral-border">
          <h3 className="text-xl font-semibold text-neutral-100 mb-4 text-center">User Status Distribution</h3>
          <div className="h-80 md:h-96"> {/* Set a fixed height for the canvas container */}
            <canvas ref={userStatusChartRef}></canvas>
          </div>
        </div>

        <div className="bg-neutral-surface p-6 rounded-lg shadow-xl border border-neutral-border">
          <h3 className="text-xl font-semibold text-neutral-100 mb-4 text-center">Post Activity (Placeholder)</h3>
          <div className="h-80 md:h-96 flex items-center justify-center text-neutral-muted">
            {/* <canvas ref={userActivityChartRef}></canvas> */}
            Chart coming soon... (e.g., Posts per day)
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminAnalyticsPage;
