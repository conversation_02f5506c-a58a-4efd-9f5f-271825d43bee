
import React from 'react';

interface IconProps {
  className?: string;
}

const ChatBubbleIcon: React.FC<IconProps> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3.68-3.091c-.91.131-1.85.192-2.82.192-2.097 0-3.992-.529-5.514-1.461a12.09 12.09 0 01-3.412-3.087 12.09 12.09 0 01-1.461-5.514c0-1.136.293-2.21.803-3.184C5.542 6.983 6.977 6.25 8.63 6.25H12.25c1.188 0 2.303.246 3.33.691.305.129.618.268.94.416m7.02 1.019a11.952 11.952 0 00-11.952-11.952H8.63C4.091 3.75 0 7.841 0 12.375c0 4.535 4.091 8.625 8.63 8.625h1.615L15.5 22.5v-3.091c.909-.08 1.784-.223 2.625-.434 4.535-1.001 7.625-5.091 7.625-9.625a11.952 11.952 0 00-.588-3.52z" />
  </svg>
);
export default ChatBubbleIcon;
