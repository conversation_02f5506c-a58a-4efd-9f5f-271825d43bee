import React, { useState } from 'react';
import { AdminMessage } from '../types';
import { useAuth } from '../hooks/useAuth';

const AdminInbox: React.FC = () => {
  const { adminMessages, markMessageAsRead, deleteAdminMessage } = useAuth();
  const [selectedMessage, setSelectedMessage] = useState<AdminMessage | null>(null);

  const handleMessageClick = (message: AdminMessage) => {
    setSelectedMessage(message);
    if (!message.isRead) {
      markMessageAsRead(message.id);
    }
  };

  const handleDeleteMessage = (messageId: string) => {
    if (window.confirm('Are you sure you want to delete this message?')) {
      deleteAdminMessage(messageId);
      if (selectedMessage?.id === messageId) {
        setSelectedMessage(null);
      }
    }
  };

  const timeSince = (dateString: string): string => {
    const date = new Date(dateString);
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + "y";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + "mo";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + "d";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + "h";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + "m";
    return Math.floor(seconds) + "s";
  };

  const unreadCount = adminMessages.filter(msg => !msg.isRead).length;

  return (
    <div className="bg-neutral-surface rounded-lg shadow-xl border border-neutral-border p-6 hover-glow cyber-border">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-brand-primary hover-text-glow transition-all duration-300">
          Admin Inbox
          {unreadCount > 0 && (
            <span className="ml-2 bg-accent-error text-white text-xs px-2 py-1 rounded-full animate-pulse-glow animate-cyber-flicker">
              {unreadCount} new
            </span>
          )}
        </h3>
      </div>

      {adminMessages.length === 0 ? (
        <p className="text-neutral-muted text-center py-8">No messages yet.</p>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Message List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {adminMessages.map((message) => (
              <div
                key={message.id}
                onClick={() => handleMessageClick(message)}
                className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:border-brand-primary hover-scale hover-glow ${
                  message.isRead
                    ? 'bg-neutral-base border-neutral-border'
                    : 'bg-brand-primary/10 border-brand-primary/50 animate-pulse-glow'
                } ${
                  selectedMessage?.id === message.id ? 'ring-2 ring-brand-primary animate-scale-in' : ''
                }`}
              >
                <div className="flex justify-between items-start mb-1">
                  <h4 className="font-semibold text-neutral-100 text-sm truncate">
                    {message.subject}
                  </h4>
                  <span className="text-xs text-neutral-muted ml-2">
                    {timeSince(message.timestamp)}
                  </span>
                </div>
                <p className="text-sm text-neutral-300 mb-1">From: {message.senderName}</p>
                <p className="text-xs text-neutral-muted truncate">
                  {message.message}
                </p>
                {!message.isRead && (
                  <div className="w-2 h-2 bg-brand-primary rounded-full mt-2"></div>
                )}
              </div>
            ))}
          </div>

          {/* Message Detail */}
          <div className="bg-neutral-base rounded-lg p-4 border border-neutral-border hover-glow cyber-border">
            {selectedMessage ? (
              <div>
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h4 className="font-semibold text-neutral-100 text-lg">
                      {selectedMessage.subject}
                    </h4>
                    <p className="text-sm text-neutral-300">
                      From: {selectedMessage.senderName}
                    </p>
                    {selectedMessage.senderEmail && (
                      <p className="text-sm text-neutral-300">
                        Email: {selectedMessage.senderEmail}
                      </p>
                    )}
                    <p className="text-xs text-neutral-muted">
                      {new Date(selectedMessage.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <button
                    onClick={() => handleDeleteMessage(selectedMessage.id)}
                    className="text-accent-error hover:text-red-400 p-1 rounded-md transition-all duration-200 hover-scale animate-cyber-flicker"
                    title="Delete Message"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                <div className="border-t border-neutral-border pt-4">
                  <p className="text-neutral-200 whitespace-pre-wrap">
                    {selectedMessage.message}
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center text-neutral-muted py-8">
                Select a message to view details
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminInbox;
