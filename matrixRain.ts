// S3Kt0R-Gram Enhanced Matrix Rain Effect

// Characters to use in the rain - Katakana, Binary, Alphanumeric, and anarcho symbols
const KATAKANA_CHARS = "アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブヅプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン";
const BINARY_CHARS = "01";
const ALPHANUMERIC_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
const ANARCHO_SYMBOLS = "ΣΠΔΦΨΩ∞≈≠≤≥∴∵∫∮∝¬⊕⊗⊘⊙⊚⊛⊝⊞⊟⊠⊡⋄⋆⋅∘∙√⚡⚠⚔⚰⚱⚿⛤⛧⛨⛩⛪⛫⛬⛭⛮⛯⛰⛱⛲⛳⛴⛵⛶⛷⛸⛹⛺⛻⛼⛽⛾⛿"; // Anarcho/cyberpunk symbols
const ALL_CHARS = KATAKANA_CHARS + BINARY_CHARS + ALPHANUMERIC_CHARS + ANARCHO_SYMBOLS;

// Enhanced color palette
const RAIN_GREEN = '#00DC82'; // brand-primary
const RAIN_PURPLE = '#A020F0'; // vibrant purple
const RAIN_CYAN = '#00FFFF'; // electric cyan
const RAIN_RED = '#FF0040'; // anarcho red
const HIGHLIGHT_COLOR = '#FFFFFF'; // pure white for highlights
// const GLOW_COLOR = '#00DC82'; // green glow (unused for now)
const BACKGROUND_FILL_COLOR = 'rgba(18, 18, 18, 0.03)'; // even more transparent for better trails

const FONT_SIZE = 16;
const MIN_SPEED = 0.5;
const MAX_SPEED = 4;
const SPAWN_RATE = 0.96; // Chance for a stream to respawn in an empty column top
const GLOW_INTENSITY = 0.8; // Glow effect intensity

interface Drop {
  x: number;
  y: number;
  speed: number;
  text: string;
  isHighlighted: boolean;
  color: string;
  opacity: number;
  glowIntensity: number;
  trailLength: number;
}

let canvas: HTMLCanvasElement;
let ctx: CanvasRenderingContext2D;
let columns: number;
let drops: Drop[];
let animationFrameId: number;

function setupCanvas() {
  canvas = document.getElementById('matrix-canvas') as HTMLCanvasElement;
  if (!canvas) {
    console.error("Matrix canvas not found!");
    return false;
  }
  ctx = canvas.getContext('2d')!;
  if (!ctx) {
    console.error("Could not get 2D context for Matrix canvas!");
    return false;
  }
  return true;
}

function resizeCanvas() {
  if (!canvas || !ctx) return;
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  columns = Math.floor(canvas.width / FONT_SIZE);
  initializeDrops();
}

function initializeDrops() {
  drops = [];
  for (let i = 0; i < columns; i++) {
    drops[i] = createDrop(i * FONT_SIZE, true); // Initial drops start at top
  }
}

function createDrop(x: number, startAtTop: boolean = false): Drop {
  const speed = Math.random() * (MAX_SPEED - MIN_SPEED) + MIN_SPEED;
  const y = startAtTop ? 0 : Math.random() * -canvas.height; // Start some off-screen initially
  const isHighlighted = Math.random() < 0.2; // Increased chance for highlights

  // Enhanced color distribution with more variety
  const colorRand = Math.random();
  let color: string;
  if (colorRand < 0.5) color = RAIN_GREEN;
  else if (colorRand < 0.75) color = RAIN_PURPLE;
  else if (colorRand < 0.9) color = RAIN_CYAN;
  else color = RAIN_RED;

  return {
    x,
    y,
    speed,
    text: ALL_CHARS[Math.floor(Math.random() * ALL_CHARS.length)],
    isHighlighted,
    color,
    opacity: Math.random() * 0.5 + 0.5, // Random opacity between 0.5 and 1
    glowIntensity: Math.random() * GLOW_INTENSITY,
    trailLength: Math.floor(Math.random() * 10) + 5, // Trail length between 5-15
  };
}

function draw() {
  if (!ctx || !canvas) return;

  // Background fill for trail effect
  ctx.fillStyle = BACKGROUND_FILL_COLOR;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  ctx.font = `${FONT_SIZE}px monospace`;

  for (let i = 0; i < drops.length; i++) {
    const drop = drops[i];

    // Enhanced drawing with glow effects
    const isLeadingChar = drop.isHighlighted && drop.y < FONT_SIZE * 8;

    if (isLeadingChar) {
      // Draw glow effect for highlighted characters
      ctx.shadowColor = drop.color;
      ctx.shadowBlur = 15 + drop.glowIntensity * 10;
      ctx.fillStyle = HIGHLIGHT_COLOR;
    } else {
      // Regular character with subtle glow
      ctx.shadowColor = drop.color;
      ctx.shadowBlur = 3 + drop.glowIntensity * 5;
      ctx.fillStyle = drop.color;
      ctx.globalAlpha = drop.opacity;
    }

    ctx.fillText(drop.text, drop.x, drop.y);

    // Reset shadow and alpha
    ctx.shadowBlur = 0;
    ctx.globalAlpha = 1;

    // Move drop with slight randomization
    drop.y += drop.speed + (Math.random() - 0.5) * 0.2;

    // Update opacity for fading effect
    if (!isLeadingChar) {
      drop.opacity *= 0.998; // Gradual fade
    }

    // Reset drop if it goes off screen or randomly
    if (drop.y > canvas.height && Math.random() > SPAWN_RATE) {
      drops[i] = createDrop(drop.x);
      drops[i].y = 0;
      drops[i].isHighlighted = Math.random() < 0.2;
    } else if (drop.y > canvas.height) {
      drops[i].y = canvas.height + FONT_SIZE * 2;
    }

    // Update character more frequently for more dynamic effect
    if (Math.random() > 0.92) {
      drop.text = ALL_CHARS[Math.floor(Math.random() * ALL_CHARS.length)];
    }

    // Occasionally change color for dynamic streams
    if (Math.random() > 0.995) {
      const colorRand = Math.random();
      if (colorRand < 0.5) drop.color = RAIN_GREEN;
      else if (colorRand < 0.75) drop.color = RAIN_PURPLE;
      else if (colorRand < 0.9) drop.color = RAIN_CYAN;
      else drop.color = RAIN_RED;
    }
  }

  animationFrameId = requestAnimationFrame(draw);
}

function startMatrixRain() {
  if (!setupCanvas()) return;

  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);

  // Ensure drops are initialized before first draw
  if (!drops || drops.length !== columns) {
      initializeDrops();
  }

  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  draw();
}

function stopMatrixRain() {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  window.removeEventListener('resize', resizeCanvas);
}

// Export functions if needed, or just call startMatrixRain from index.tsx
export { startMatrixRain, stopMatrixRain };
