import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

const firebaseConfig = {
  apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
  authDomain: "s3kt0r-30ede.firebaseapp.com",
  projectId: "s3kt0r-30ede",
  storageBucket: "s3kt0r-30ede.firebasestorage.app",
  messagingSenderId: "246065925479",
  appId: "1:246065925479:web:4c7a0f2660fcc9880af3c2",
  measurementId: "G-1R3KWWKXBM"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;
