
import React from 'react';

interface IconProps {
  className?: string;
}

const TrashIcon: React.FC<IconProps> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.24.086 3.323.255A1.125 1.125 0 009.75 7.22v1.727c0 .414.336.75.75.75h3c.414 0 .75-.336.75-.75V7.22c0-.318.238-.593.55-.681A48.097 48.097 0 0112 6.5m0 0c2.677 0 5.175.37 7.444 1.036M6.528 9.184L6.75 19.5m3-10.316V19.5m3-10.316V19.5m0 0H14.25" />
  </svg>
);
export default TrashIcon;
