<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Admin Account</title>
</head>
<body>
    <h1>Setup Admin Account for S3Kt0R-Gram</h1>
    <button id="setupAdmin">Create Admin Account</button>
    <div id="result"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, createUserWithEmailAndPassword, updateProfile } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBz12g2Ilty-HnKNCLq4rD9q0bMivjcGUM",
            authDomain: "s3kt0r-30ede.firebaseapp.com",
            projectId: "s3kt0r-30ede",
            storageBucket: "s3kt0r-30ede.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:4c7a0f2660fcc9880af3c2",
            measurementId: "G-1R3KWWKXBM"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        document.getElementById('setupAdmin').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Creating admin account...';

            try {
                const adminEmail = '<EMAIL>';
                const adminPassword = 'Tr1p$t0p3301';
                const adminUsername = 'S3Kt0R_Overseer';

                // Create the user account
                const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
                const firebaseUser = userCredential.user;

                // Update the user's display name
                await updateProfile(firebaseUser, { displayName: adminUsername });

                // Create admin user document in Firestore
                const userData = {
                    id: firebaseUser.uid,
                    username: adminUsername,
                    avatarUrl: `https://picsum.photos/seed/${adminUsername.replace(/\s+/g, '')}/100/100`,
                    isActive: true,
                    isPendingApproval: false,
                    isAdmin: true,
                    bio: 'Platform Administrator\\nGuardian of the digital realm\\nMaintaining order in chaos\\n⚠️ With great power...'
                };

                await setDoc(doc(db, 'users', firebaseUser.uid), userData);

                resultDiv.innerHTML = `
                    <h2>Admin account created successfully!</h2>
                    <p><strong>Email:</strong> ${adminEmail}</p>
                    <p><strong>Username:</strong> ${adminUsername}</p>
                    <p><strong>User ID:</strong> ${firebaseUser.uid}</p>
                    <p>The admin can now log in to the application.</p>
                `;

            } catch (error) {
                console.error('Error creating admin account:', error);
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
